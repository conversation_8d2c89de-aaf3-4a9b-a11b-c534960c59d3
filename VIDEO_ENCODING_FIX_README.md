# Video Encoding Fix - File Permission Error Solution

## Problem Description

Users were experiencing intermittent permission errors during video processing, specifically when the encoding fix feature was enabled. The error occurred after video generation was complete:

```
❌ Error processing [file]: [<PERSON>rrno 13] Permission denied: 'output_file.mp4'
FileExistsError: [WinError 183] Cannot create a file when that file already exists
```

## Root Cause Analysis

The issue was caused by a race condition in the video encoding fix process:

1. **Video Generation**: A video file is created (e.g., `video_25s.mp4`)
2. **End Frame Extraction**: `cv2.VideoCapture` opens the video file to extract the last frame
3. **File Handle Release**: `cv2.VideoCapture.release()` is called, but on Windows, the file handle isn't always immediately released
4. **Encoding Fix**: FFmpeg creates a fixed version (`video_25s_fixed.mp4`)
5. **File Replacement**: `shutil.move(fixed_file, original_file)` tries to replace the original file
6. **Permission Error**: The original file is still locked, causing the permission error

## Solution Implemented

### New Function: `replace_video_file_safely()`

A robust file replacement function with the following features:

- **Retry Mechanism**: Attempts replacement up to 5 times with exponential backoff
- **Garbage Collection**: Forces Python garbage collection to help release file handles
- **Graceful Fallback**: If replacement fails, saves the fixed version with a `_fixed.mp4` suffix
- **Error Handling**: Comprehensive error handling for different failure scenarios

### Files Modified

1. **`batch_f1.py`** - Main F1 batch processing script
2. **`batch_f1_video.py`** - F1 video processing script  
3. **`batch.py`** - Standard batch processing script

### Code Changes

**Before:**
```python
# Handle encoding fix
if fix_encoding:
    fixed_output = fix_video_encoding(final_output_filename)
    if fixed_output:
        shutil.move(fixed_output, final_output_filename)  # ❌ Could fail
```

**After:**
```python
# Handle encoding fix
if fix_encoding:
    fixed_output = fix_video_encoding(final_output_filename)
    if fixed_output:
        success = replace_video_file_safely(fixed_output, final_output_filename)  # ✅ Robust
        if success:
            print(f"✅ Successfully processed and fixed {image_path} -> {final_output_filename}")
        else:
            print(f"Warning: Could not replace original with fixed version. Keeping both files.")
```

## Benefits

1. **Eliminates Permission Errors**: The retry mechanism handles temporary file locks
2. **Graceful Degradation**: If replacement fails, users still get the fixed video file
3. **No Data Loss**: Original processing continues even if file replacement fails
4. **Better User Experience**: Clear messaging about what happened

## Testing

A test script (`test_video_file_replacement.py`) was created to verify the fix works correctly:

- ✅ Normal file replacement works
- ✅ Locked file replacement with retry mechanism works
- ✅ Fallback mechanism creates alternative file when needed

## Usage Notes

- The fix is automatically applied - no user configuration needed
- If replacement fails, look for files with `_fixed.mp4` suffix in the outputs folder
- The retry mechanism adds a small delay (0.5-2 seconds) in rare failure cases
- Original functionality is preserved - this only makes the process more robust

## Technical Details

The `replace_video_file_safely()` function:
- Uses exponential backoff: 0.5s, 0.75s, 1.125s, 1.6875s, 2.53s delays
- Calls `gc.collect()` between attempts to help release file handles
- Handles both `PermissionError` and `FileExistsError` exceptions
- Provides detailed logging for troubleshooting

This fix resolves the intermittent permission errors while maintaining full backward compatibility.
